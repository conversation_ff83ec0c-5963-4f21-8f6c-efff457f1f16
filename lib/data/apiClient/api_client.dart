// ignore_for_file: 

import 'package:sfm_new/core/app_export.dart';
import 'package:dio/dio.dart';

class ApiClient extends GetConnect {
  static const String _baseUrl = 'http://192.168.0.101/sfm_v2_laravel/public/';

  // static const String _baseUrl = 'https://neckv2.myebooks.online/';

  static final Dio dioClient = Dio(
    BaseOptions(
      connectTimeout: Duration(seconds: 50),
      receiveTimeout: Duration(seconds: 50),
      sendTimeout: Duration(seconds: 50),
    ),
  );

  static const String _api = 'api/';

  static const String _imgProductBaseUrl = _baseUrl + productImageEndpoint;

  static const String _imgClaimComplainBaseUrl =
      _baseUrl + claimComplainImageEndpoint;

  static const String _imgExpenseBaseUrl = _baseUrl + expenseImageEndpoint;

  static const String _imgPaymentCollectionBaseUrl =
      _baseUrl + paymentCollectionImageEndpoint;

  static const String _imgRetailerBaseUrl = _baseUrl + retailerImageEndpoint;

  static const String _imgBannerBaseUrl = _baseUrl + bannerImageEndpoint;

  static const String loginEndpoint = 'login';
  static const String checkinEndpoint = 'attendance/checkin';
  static const String checkoutEndpoint = 'attendance/checkout';
  static const String getTerritoryEndpoint = 'sync_service/get_territory';

  static const String productImageEndpoint = "uploads/product_images/";
  static const String claimComplainImageEndpoint =
      "uploads/claim_complain_images/";
  static const String expenseImageEndpoint = "uploads/expense_images/";
  static const String paymentCollectionImageEndpoint =
      "uploads/payment_collection_images/";
  static const String retailerImageEndpoint = "uploads/retailer_images/";
  static const String bannerImageEndpoint = "uploads/app_banner/";

  static const String getProductBrandEndpoint =
      'sync_service/get_product_brand';
  static const String getProductTypeEndpoint = 'sync_service/get_product_type';
  static const String getUOMEndpoint = 'sync_service/get_uom';

  static const String getPriceGroupEndpoint = 'sync_service/get_price_group';
  static const String getProductEndpoint = 'sync_service/get_product';
  static const String getMarketTypeEndpoint = 'sync_service/get_market_type';

  static const String getCustomerCategoryEndpoint =
      'sync_service/get_customer_category';

  static const String getGradeEndpoint = 'sync_service/get_grade';
  static const String getBeatRouteEndpoint = 'sync_service/get_beat_route';

  static const String getCustomerEndpoint = 'sync_service/get_customer';
  static const String getBankEndpoint = 'sync_service/get_bank';
  static const String getClaimComplainTypeEndpoint =
      'sync_service/get_claim_complain_type';

  static const String getClaimComplainEndpoint =
      'sync_service/get_claim_complain';
  static const String getExpenseTypeEndpoint = 'sync_service/get_expense_type';
  static const String getExpenseEndpoint =
      'sync_service/get_expense?selected_date=';

  static const String getNonProductiveReasonEndpoint =
      'sync_service/get_nonproductive_reason';
  static const String getPaymentTypeEndpoint = 'sync_service/get_payment_type';
  static const String getPaymentConditionEndpoint =
      'sync_service/get_payment_condition';

  static const String getPaymentCollectionEndpoint =
      'sync_service/get_payment_collection?selected_date=';
  static const String getLeaveEndpoint = 'sync_service/get_leave_application';

  static const String getCallEndpoint =
      'sync_service/get_call_master?selected_date=';

  static const String getInvoiceEndpoint =
      'sync_service/get_invoice?call_code=';

  static const String getAddManualBeatEndpoint = 'add_beat_manually';

  static const String getReportEndpoint = 'sync_service/get_report';

  static const String getConfigEndpoint = 'sync_service/get_config';

  static const String getLanguageEndpoint = 'sync_service/get_language';

  static const String getDashboardDataEndpoint =
      'sync_service/get_dashboard_data';

  static const String getNotificationEndpoint = 'sync_service/get_notification';

  // {{domain-name}}/api/sync_service/get_invoice?call_code=09e1312d-4aab-4063-afc5-a513b5f1213a

  static const String getOrderEndpoint = 'sync_service/get_order_master';

  static const String postAddComplainEndpoint = 'claim_complain/add';

  static const String postAddCustomerEndpoint = 'customer/add';

  static const String postUpdateCustomerEndpoint = 'customer/update';

  static const String postAddExpenseEndpoint = 'expense/add';

  static const String postAddBeatEndpoint = 'beat/add';

  static const String postAddPaymentEndpoint = 'payment/add';

  static const String postAddLeaveEndpoint = 'leave/add';

  static const String postAddOrderEndpoint = 'order/add';

  static const String postFCMTokenEndpoint = 'fcm/update';

  static const String getRoutePlanListEndpoint = 'route_plan/list';

  static const String postAddRoutePlanEndpoint = 'route_plan/add';

  static const String getRoutePlanBeatListEndpoint = 'route_plan/beat_list';

  static const String getTeamMemberListEndpoint = 'employee/team_member';

  static const String getTeamReportListEndpoint = 'reports/team_report_list';

  static const String postTeamReportEndpoint = 'reports/team_reports';

  static String get baseURL => _baseUrl;

  static String get imgProductBaseURL => _imgProductBaseUrl;
  static String get imgClaimComplainBaseURL => _imgClaimComplainBaseUrl;
  static String get imgPaymentCollectionBaseURL => _imgPaymentCollectionBaseUrl;
  static String get imgExpenseBaseURL => _imgExpenseBaseUrl;
  static String get imgRetailerBaseURL => _imgRetailerBaseUrl;
  static String get imgBannerBaseURL => _imgBannerBaseUrl;

  static String getUrl(String endpoint) => _baseUrl + _api + endpoint;
}
