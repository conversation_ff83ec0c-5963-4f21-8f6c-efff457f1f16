import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'controller/add_retailer_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddRetailerScreen extends GetWidget<AddRetailerController> {
  AddRetailerScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 16.h,
              vertical: 15.v,
            ),
            child: Obx(() {
              if (controller.customerList.length > 0) {
                EasyLoading.dismiss();
                return SingleChildScrollView(
                  child: Column(
                    children: [
                      CustomAllTextField(
                        controller: controller.shopNameController,
                        hintText: "Shop Name",
                        height: 55,
                        showError: controller.showError.value,
                        isValid: controller.isShopNameValid.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomAllTextField(
                        controller: controller.contactPersonController,
                        hintText: "Contact Person Name",
                        height: 55,
                        showError: controller.showError.value,
                        isValid: controller.isContactPersonValid.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown.search(
                        decoration: CustomDropdownDecoration(
                          searchFieldDecoration: SearchFieldDecoration(
                              textStyle:
                                  CustomTextStyles.titleSmallBluegray900),
                          noResultFoundStyle:
                              CustomTextStyles.titleSmallBluegray900,
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                          errorStyle: CustomTextStyles.titleSmallBluegray900,
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          closedErrorBorder: Border.all(color: Colors.red),
                          closedErrorBorderRadius: BorderRadius.circular(12),
                        ),
                        hintText: 'Select Distributor/Dealer',
                        searchHintText: 'Select Distributor/Dealer',
                        items: controller.customerList
                            .map((cm) => cm.cmName!)
                            .toList(),
                        excludeSelected: false,
                        initialItem: controller.isFromProductShopInfo.value ==
                                    true &&
                                controller.selectedDistDealerName.value != ''
                            ? "${controller.selectedDistDealerName.value}"
                            : "Select Distributor/Dealer",
                        onChanged: (selectedType) {
                          FocusManager.instance.primaryFocus?.unfocus();
                          print('Selected Distributor/Dealer: $selectedType');
                          final selectedCustomerCode = controller
                              .getCustomerCodeFromName('${selectedType}');
                          controller.selectedCustomerCode.value =
                              selectedCustomerCode ?? "";
                          print("${controller.selectedCustomerCode.value}");
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: 'Select Grade',
                        initialItem:
                            controller.isFromProductShopInfo.value == true
                                ? "${controller.selectedGradeName.value}"
                                : "Select Grade",
                        items: controller.gradeList
                            .map((gm) => gm.gm_name!)
                            .toList(),
                        onChanged: (selectedType) {
                          FocusManager.instance.primaryFocus?.unfocus();
                          print('Selected Grade: $selectedType');
                          final selectedGradeID =
                              controller.getGradeIDFromName('${selectedType}');
                          controller.selectedGradeID.value =
                              selectedGradeID ?? 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: 'Select Category',
                        initialItem:
                            controller.isFromProductShopInfo.value == true
                                ? "${controller.selectedCategoryName.value}"
                                : "Select Category",
                        items: controller.customerCategoryList
                            .map((cm) => cm.ccmName!)
                            .toList(),
                        onChanged: (selectedType) {
                          print('Selected Category: $selectedType');
                          final selectedCategoryID = controller
                              .getCustomerCategoryIDFromName('${selectedType}');
                          controller.selectedCategoryID.value =
                              selectedCategoryID ?? 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                          closedErrorBorder: Border.all(color: Colors.red),
                          closedErrorBorderRadius: BorderRadius.circular(12),
                        ),
                        hintText: 'Select State',
                        initialItem:
                            controller.isFromProductShopInfo.value == true
                                ? "${controller.selectedStateName.value}"
                                : "Select State",
                        items: controller.stateList
                            .map((cm) => cm.state_name!)
                            .toList(),
                        onChanged: (value) {
                          print('Selected State: $value');
                          final selectedStateID =
                              controller.getStateIDFromName('${value}');
                          controller.selectedStateID.value =
                              selectedStateID ?? 0;
                          controller.selectedDistrictID.value = 0;
                          controller.selectedTownID.value = 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown.search(
                        decoration: CustomDropdownDecoration(
                          searchFieldDecoration: SearchFieldDecoration(
                              textStyle:
                                  CustomTextStyles.titleSmallBluegray900),
                          noResultFoundStyle:
                              CustomTextStyles.titleSmallBluegray900,
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                        ),
                        hintText: 'Select District',
                        searchHintText: "Select District",
                        items: controller.districtList
                            .where((st) =>
                                st.state_id == controller.selectedStateID.value)
                            .map((cm) => cm.dm_name!)
                            .toList(),
                        excludeSelected: false,
                        initialItem:
                            controller.isFromProductShopInfo.value == true
                                ? "${controller.selectedDistrictName.value}"
                                : "Select District",
                        onChanged: (selectedType) {
                          print('Selected District: $selectedType');
                          final selectedDistrictID = controller
                              .getDistrictIDFromName('${selectedType}');
                          controller.selectedDistrictID.value =
                              selectedDistrictID ?? 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown.search(
                        decoration: CustomDropdownDecoration(
                          searchFieldDecoration: SearchFieldDecoration(
                              textStyle:
                                  CustomTextStyles.titleSmallBluegray900),
                          noResultFoundStyle:
                              CustomTextStyles.titleSmallBluegray900,
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                        ),
                        hintText: 'Select Town',
                        searchHintText: 'Select Town',
                        items: controller.townList
                            .where((dt) =>
                                dt.district_id ==
                                controller.selectedDistrictID.value)
                            .map((cm) => cm.town_name!)
                            .toList(),
                        excludeSelected: false,
                        initialItem:
                            controller.isFromProductShopInfo.value == true
                                ? "${controller.selectedTownName.value}"
                                : "Select Town",
                        onChanged: (selectedType) {
                          print('Selected Town : $selectedType');
                          final selectedTownID =
                              controller.getTownIDFromName('${selectedType}');
                          controller.selectedTownID.value = selectedTownID ?? 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomAllTextField(
                        controller: controller.addressController,
                        hintText: "Address",
                        height: 55,
                        showError: controller.showError.value,
                        isValid: controller.isAddressValid.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomAllTextField(
                        controller: controller.areaController,
                        hintText: "Area",
                        height: 55,
                        showError: controller.showError.value,
                        isValid: controller.isAreaValid.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomAllTextField(
                        controller: controller.pincodeController,
                        maxLength: 6,
                        hintText: "Pincode",
                        height: 55,
                        keyboardType: TextInputType.number,
                        showError: controller.showError.value,
                        isValid: controller.isPincodeValid.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomMobile1TextField(
                        controller: controller.mobileNo1Controller,
                        maxLength: 10,
                        hintText: "Mobile No 1",
                        keyboardType: TextInputType.phone,
                        height: 55,
                        isRequired: true,
                        isValid: controller.isMobile1Valid.value,
                        showError: controller.showError.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomGSTTextField(
                        controller: controller.gstController,
                        hintText: "GST No (Optional)",
                        height: 55,
                        isValid: controller.isGSTValid.value,
                        showError: controller.showError.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomMobile1TextField(
                        controller: controller.mobileNo2Controller,
                        maxLength: 10,
                        hintText: "Mobile No 2 (Optional)",
                        keyboardType: TextInputType.phone,
                        height: 55,
                        isRequired: false,
                        isValid: controller.isMobile2Valid.value,
                        showError: controller.showError.value,
                      ),
                      SizedBox(height: 10.v),
                      CustomTextField(
                        controller: controller.emailController,
                        hintText: "Email (Optional)",
                        keyboardType: TextInputType.emailAddress,
                        height: 55,
                      ),
                      SizedBox(height: 10.v),
                      CustomTextFieldPackagingCharge(
                        controller: controller.birthDateController,
                        hintText: "Birth Date (Optional)",
                        height: 55,
                      ),
                      SizedBox(height: 10.v),
                      CustomTextFieldPackagingCharge(
                        controller: controller.anniDateController,
                        hintText: "Anniversary Date (Optional)",
                        height: 55,
                      ),
                      SizedBox(height: 10.v),
                      CustomTextField(
                        controller: controller.panController,
                        hintText: "PAN No (Optional)",
                        height: 55,
                      ),
                      SizedBox(height: 20.v),
                      Text(
                        "Upload Shop Photo",
                        style: CustomTextStyles.titleSmallBluegray900,
                      ),
                      SizedBox(height: 10.v),
                      _buildBorderedContainer(context),
                      SizedBox(height: 10.v),
                    ],
                  ),
                );
              } else {
                EasyLoading.show(status: 'Loading...');
                return Container();
              }
            }),
          ),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  Widget _buildBorderedContainer(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            print("1st image tapped");
            controller.selectedImageNo.value = 1;
            controller.requestCameraPermission();
          },
          child: Container(
            width: MediaQuery.of(context).size.width * 0.26,
            height: 100,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade300,
                width: 2.0,
              ),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Center(
              child: Obx(
                () {
                  final File? image = controller.imageFile1.value;
                  print("updated image: ${image}");

                  String? imageUrl = "";
                  if (controller.updateShopImageList.length > 0) {
                    print(
                        "updateShopImageList: ${controller.updateShopImageList.length}");
                    imageUrl = controller.updateShopImageList[0];
                  } else {}
                  if (controller.isFromProductShopInfo.value)
                  if (image != null) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(12.0),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.file(
                            image,
                            width: MediaQuery.of(context).size.width * 0.26,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    );
                  } else {
                    int? imgLength = imageUrl.length;
                    print("imgLength: ${imgLength}");
                    String loadedImageUrl =
                        "${ApiClient.imgRetailerBaseURL}${imageUrl}";
                    print("loadedImageUrl: ${loadedImageUrl}");

                    if (imageUrl.isEmpty) {
                      return Container(
                        child: Center(
                          child: CustomImageView(
                            imagePath: ImageConstant.imgCameraToTakePhotos,
                            height: 20.adaptSize,
                            width: 20.adaptSize,
                          ),
                        ),
                      );
                    } else if (imgLength < 75) {
                      print("url image called");
                      return Image.network(
                        loadedImageUrl,
                        width: MediaQuery.of(context).size.width * 0.26,
                        fit: BoxFit.contain,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(child: Text('Error loading image 123'));
                        },
                      );
                    } else {
                      print("url image called");
                      return Image.memory(
                        base64Decode(imageUrl),
                        fit: BoxFit.cover, 
                        width: MediaQuery.of(context).size.width * 0.26,
                      );
                    }
                  }
                  else if (image != null)
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(12.0),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.file(
                            image,
                            width: MediaQuery.of(context).size.width * 0.26,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    );
                  else
                    return Container(
                      child: Center(
                        child: CustomImageView(
                          imagePath: ImageConstant.imgCameraToTakePhotos,
                          height: 20.adaptSize,
                          width: 20.adaptSize,
                        ),
                      ),
                    );
                },
              ),
            ),
          ),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            print("2nd image tapped");
            controller.selectedImageNo.value = 2;
            controller.requestCameraPermission();
          },
          child: Container(
            width: MediaQuery.of(context).size.width * 0.26,
            height: 100,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade300,
                width: 2.0,
              ),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Center(
              child: Obx(
                () {
                  final File? image = controller.imageFile2.value;
                  if (image != null)
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(12.0),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.file(
                            image,
                            width: MediaQuery.of(context).size.width * 0.26,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    );
                  else
                    return Container(
                      child: Center(
                        child: CustomImageView(
                          imagePath: ImageConstant.imgCameraToTakePhotos,
                          height: 20.adaptSize,
                          width: 20.adaptSize,
                        ),
                      ),
                    );
                },
              ),
            ),
          ),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            print("3rd image tapped");
            controller.selectedImageNo.value = 3;
            controller.requestCameraPermission();
          },
          child: Container(
            width: MediaQuery.of(context).size.width * 0.26,
            height: 100,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade300,
                width: 2.0,
              ),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Center(
              child: Obx(
                () {
                  final File? image = controller.imageFile3.value;
                  if (image != null)
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(12.0),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.file(
                            image,
                            width: MediaQuery.of(context).size.width * 0.26,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    );
                  else
                    return Container(
                      child: Center(
                        child: CustomImageView(
                          imagePath: ImageConstant.imgCameraToTakePhotos,
                          height: 20.adaptSize,
                          width: 20.adaptSize,
                        ),
                      ),
                    );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: Obx(
        () => AppbarSubtitleFour(
          text: controller.isFromProductShopInfo.value
              ? "Update Shop"
              : "Add Shop",
        ),
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return Obx(
      () => CustomElevatedButton(
        text: controller.isFromProductShopInfo.value ? "Update" : "Submit",
        margin: EdgeInsets.only(
          left: 25.h,
          right: 25.h,
          bottom: 24.v,
        ),
        onPressed: () {
          FocusManager.instance.primaryFocus?.unfocus;
          addRetailer();
        },
      ),
    );
  }

  void addRetailer() {
    controller.showError.value = true;
    controller.validateAllFields();

    int imageCount = controller.shopImageValue.value;
    if (controller.isFromProductShopInfo.value) {
      print("imageCount: ${imageCount}");
      print(
          "controller.shopImageList.length: ${controller.shopImageList.length}");
      print("controller.imageFile1.value: ${controller.imageFile1.value}");

      final regex = RegExp(
          r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$');
      String gstValue = controller.gstController.value.text;
      if (controller.shopNameController.value.text.isEmpty) {
        showToastMessage("Please enter shop name");
      } else if (controller.contactPersonController.value.text.isEmpty) {
        showToastMessage("Please enter contact person name");
      } else if (controller.selectedCustomerCode.value == "") {
        showToastMessage("Please select distributor/dealer");
      } else if (controller.selectedGradeID.value == 0) {
        showToastMessage("Please select grade");
      } else if (controller.selectedCategoryID.value == 0) {
        showToastMessage("Please select category");
      } else if (controller.selectedStateID.value == 0) {
        showToastMessage("Please select state");
      } else if (controller.selectedDistrictID.value == 0) {
        showToastMessage("Please select district");
      } else if (controller.selectedTownID.value == 0) {
        showToastMessage("Please select town");
      } else if (controller.addressController.value.text.isEmpty) {
        showToastMessage("Please enter address");
      } else if (controller.areaController.value.text.isEmpty) {
        showToastMessage("Please enter area");
      } else if (controller.pincodeController.value.text.isEmpty) {
        showToastMessage("Please enter pincode");
      } else if (controller.mobileNo1Controller.value.text.isEmpty) {
        showToastMessage("Please enter mobile number");
      } else if (controller.mobileNo1Controller.value.text.length != 10) {
        showToastMessage("Please enter valid mobile number");
      } else if (controller.mobileNo2Controller.value.text.isNotEmpty &&
          controller.mobileNo2Controller.value.text.length != 10) {
        showToastMessage("Please enter valid mobile number");
      }
      // else if (controller.gstController.value.text.isEmpty) {
      //   showToastMessage("Please enter gst number");
      // }
      else if (gstValue.isNotEmpty && !regex.hasMatch(gstValue)) {
        showToastMessage("Please enter a valid GST number");
      } else if (imageCount == 1 &&
          controller.updateShopImageList.length != 1 &&
          (controller.imageFile1.value == null)) {
        showToastMessage("Please add shop photo");
      } else if (imageCount == 2 &&
          controller.updateShopImageList.length != 2 &&
          (controller.imageFile1.value == null ||
              controller.imageFile2.value == null)) {
        showToastMessage("Please add shop photo");
      } else if (imageCount == 3 &&
          controller.updateShopImageList.length != 3 &&
          (controller.imageFile1.value == null ||
              controller.imageFile2.value == null ||
              controller.imageFile3.value == null)) {
        showToastMessage("Please add shop photo");
      } else {
        print("shop name: ${controller.shopNameController.value.text}");
        print(
            "contact person: ${controller.contactPersonController.value.text}");
        print("customer code: ${controller.selectedCustomerCode.value}");
        print("grade id: ${controller.selectedGradeID.value}");
        print("category id: ${controller.selectedCategoryID.value}");
        print("state id: ${controller.selectedStateID.value}");
        print("district id: ${controller.selectedDistrictID.value}");
        print("town id: ${controller.selectedTownID.value}");
        print("address: ${controller.addressController.value.text}");
        print("mobile: ${controller.mobileNo1Controller.value.text}");
        print("gst: ${controller.gstController.value.text}");
        print("pan: ${controller.panController.text}");
        print("latitude: ${controller.lat.value}");
        print("longitude: ${controller.long.value}");
        print("area: ${controller.areaController.value.text}");
        print("cm type: ${controller.addressController.value.text}");

        print("beat code: ${controller.selectedBeatCode.value}");
        print("birth date: ${controller.birthDateController.text}");
        print("anniversary date: ${controller.anniDateController.text}");
        print("shop images: ${controller.shopImageList}");
        print("print add customer to db");

        controller.updateCustomer(controller.selectedRetailerCode.value);
      }
    } else {
      final regex = RegExp(
          r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$');
      String gstValue = controller.gstController.value.text;
      if (controller.shopNameController.value.text.isEmpty) {
        showToastMessage("Please enter shop name");
      } else if (controller.contactPersonController.value.text.isEmpty) {
        showToastMessage("Please enter contact person name");
      } else if (controller.selectedCustomerCode.value == "") {
        showToastMessage("Please select distributor/dealer");
      } else if (controller.selectedGradeID.value == 0) {
        showToastMessage("Please select grade");
      } else if (controller.selectedCategoryID.value == 0) {
        showToastMessage("Please select category");
      } else if (controller.selectedStateID.value == 0) {
        showToastMessage("Please select state");
      } else if (controller.selectedDistrictID.value == 0) {
        showToastMessage("Please select district");
      } else if (controller.selectedTownID.value == 0) {
        showToastMessage("Please select town");
      } else if (controller.addressController.value.text.isEmpty) {
        showToastMessage("Please enter address");
      } else if (controller.areaController.value.text.isEmpty) {
        showToastMessage("Please enter area");
      } else if (controller.pincodeController.value.text.isEmpty) {
        showToastMessage("Please enter pincode");
      } else if (controller.mobileNo1Controller.value.text.isEmpty) {
        showToastMessage("Please enter mobile number");
      } else if (controller.mobileNo1Controller.value.text.length != 10) {
        showToastMessage("Please enter valid mobile number");
      } else if (controller.mobileNo2Controller.value.text.isNotEmpty &&
          controller.mobileNo2Controller.value.text.length != 10) {
        showToastMessage("Please enter valid mobile number");
      }
      // else if (controller.gstController.value.text.isEmpty) {
      //   showToastMessage("Please enter gst number");
      // }
      else if (gstValue.isNotEmpty && !regex.hasMatch(gstValue)) {
        showToastMessage("Please enter a valid GST number");
      } else if (imageCount == 1 && (controller.imageFile1.value == null)) {
        showToastMessage("Please add shop photo");
      } else if (imageCount == 2 &&
          (controller.imageFile1.value == null ||
              controller.imageFile2.value == null)) {
        showToastMessage("Please add shop photo");
      } else if (imageCount == 3 &&
          (controller.imageFile1.value == null ||
              controller.imageFile2.value == null ||
              controller.imageFile3.value == null)) {
        showToastMessage("Please add shop photo");
      } else {
        print("shop name: ${controller.shopNameController.value.text}");
        print(
            "contact person: ${controller.contactPersonController.value.text}");
        print("customer code: ${controller.selectedCustomerCode.value}");
        print("grade id: ${controller.selectedGradeID.value}");
        print("category id: ${controller.selectedCategoryID.value}");
        print("state id: ${controller.selectedStateID.value}");
        print("district id: ${controller.selectedDistrictID.value}");
        print("town id: ${controller.selectedTownID.value}");
        print("address: ${controller.addressController.value.text}");
        print("mobile: ${controller.mobileNo1Controller.value.text}");
        print("gst: ${controller.gstController.value.text}");
        print("pan: ${controller.panController.text}");
        print("latitude: ${controller.lat.value}");
        print("longitude: ${controller.long.value}");
        print("area: ${controller.areaController.value.text}");
        print("cm type: ${controller.addressController.value.text}");

        print("beat code: ${controller.selectedBeatCode.value}");
        print("birth date: ${controller.birthDateController.text}");
        print("anniversary date: ${controller.anniDateController.text}");
        print("shop images: ${controller.shopImageList}");
        print("print add customer to db");

        controller.addRetailerToDB();
      }
    }
  }

  bool isValidMobile(String mobile) {
    // Add your password validation logic here
    return mobile.isNotEmpty && mobile.length == 10;
  }
}

class CustomTextFieldPackagingCharge extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  final Function(bool)? onChanged;

  CustomTextFieldPackagingCharge({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.readOnly = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _showDatePicker(context);
      },
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                readOnly: readOnly,
                style: CustomTextStyles.titleSmallBluegray900,
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle:
                      hintStyle ?? CustomTextStyles.titleSmallBluegray900,
                  contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
                  border: InputBorder.none,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 12),
              child: CustomImageView(
                svgPath: ImageConstant.imgCalendarPrimary,
                height: 20.v,
                width: 20.h,
                margin: EdgeInsets.symmetric(vertical: 3.v),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDatePicker(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: theme.colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    ).then((value) {
      if (value != null) {
        print("date value: $value");
        String dateValue = value.toString();
        final AddRetailerController retailerController =
            Get.put(AddRetailerController());
        if (controller == retailerController.birthDateController) {
          retailerController.birthDateToShow.value =
              retailerController.formatDateTimeToShow(dateValue);
          retailerController.birthDateToSave.value =
              retailerController.formatDateTimeToSave(dateValue);

          print("birthDateToShow: ${retailerController.birthDateToShow.value}");
          print("birthDateToSave: ${retailerController.birthDateToSave.value}");
        } else {
          retailerController.anniDateToShow.value =
              retailerController.formatDateTimeToShow(dateValue);
          retailerController.anniDateToSave.value =
              retailerController.formatDateTimeToSave(dateValue);

          print("anniDateToShow: ${retailerController.anniDateToShow.value}");
          print("anniDateToSave: ${retailerController.anniDateToSave.value}");
        }
      }
    });
  }
}

class CustomMobile1TextField extends StatelessWidget {
  final AddRetailerController retailerController =
      Get.put(AddRetailerController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;
  final bool isRequired;

  CustomMobile1TextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid
                ? isRequired
                    ? Colors.red
                    : (controller.value.text.isNotEmpty)
                        ? Colors.red
                        : Colors.grey.shade300
                : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          maxLength: maxLength,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          onChanged: (value) {
            if (isRequired) {
              retailerController.validateMobileNumber1();
            } else {
              retailerController.validateMobileNumber2();
            }
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            errorText: showError && !isValid
                ? isRequired
                    ? 'Please enter a valid 10-digit phone number'
                    : (controller.value.text.isNotEmpty
                        ? 'Please enter a valid 10-digit phone number'
                        : null)
                : null,
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class CustomGSTTextField extends StatelessWidget {
  final AddRetailerController retailerController =
      Get.put(AddRetailerController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;

  CustomGSTTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid && controller.value.text.isNotEmpty
                ? Colors.red
                : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          textCapitalization: TextCapitalization.characters,
          maxLength: maxLength,
          inputFormatters: [
            UpperCaseTextFormatter(),
          ],
          onChanged: (value) {
            retailerController.validateGST();
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class CustomAllTextField extends StatelessWidget {
  final AddRetailerController retailerController =
      Get.put(AddRetailerController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;

  CustomAllTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid ? Colors.red : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          maxLength: maxLength,
          inputFormatters: [
            if (keyboardType == TextInputType.text)
              FilteringTextInputFormatter.singleLineFormatter,
            if (keyboardType == TextInputType.phone)
              FilteringTextInputFormatter.digitsOnly,
          ],
          onChanged: (value) {
            retailerController.validateField(
                retailerController.shopNameController.value.text,
                retailerController.isShopNameValid);
            retailerController.validateField(
                retailerController.contactPersonController.value.text,
                retailerController.isContactPersonValid);
            retailerController.validateField(
                retailerController.addressController.value.text,
                retailerController.isAddressValid);
            retailerController.validateField(
                retailerController.areaController.value.text,
                retailerController.isAreaValid);
            retailerController.validateField(
                retailerController.pincodeController.value.text,
                retailerController.isPincodeValid);
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            errorText:
                showError && !isValid ? 'This field cannot be empty' : null,
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class CustomAllIDTextField extends StatelessWidget {
  final AddRetailerController retailerController =
      Get.put(AddRetailerController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;

  CustomAllIDTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid ? Colors.red : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          maxLength: maxLength,
          inputFormatters: [
            if (keyboardType == TextInputType.text)
              FilteringTextInputFormatter.singleLineFormatter,
            if (keyboardType == TextInputType.phone)
              FilteringTextInputFormatter.digitsOnly,
          ],
          onChanged: (value) {
            retailerController.validateAllFields();
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            errorText:
                showError && !isValid ? 'This field cannot be empty' : null,
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
        text: newValue.text.toUpperCase(), selection: newValue.selection);
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        keyboardType: keyboardType,
        maxLength: maxLength,
        decoration: InputDecoration(
          counterText: "",
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
