// ignore_for_file: unnecessary_null_comparison

import 'package:sfm_new/core/utils/flutter_toast_message.dart';

import 'controller/add_leave_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddLeaveScreen extends GetWidget<AddLeaveController> {
  AddLeaveScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 16.h,
              vertical: 15.v,
            ),
            child: Column(
              children: [
                CustomDropdown<String>(
                  decoration: CustomDropdownDecoration(
                    expandedFillColor: Colors.white,
                    expandedBorder: Border.all(color: Colors.grey[300]!),
                    expandedBorderRadius: BorderRadius.circular(12),
                    closedBorder: Border.all(color: Colors.grey[300]!),
                    closedBorderRadius: BorderRadius.circular(12),
                    listItemStyle: CustomTextStyles.titleSmallBluegray900,
                    headerStyle: CustomTextStyles.titleSmallBluegray900,
                  ),
                  hintText: 'Select Leave Type',
                  initialItem: 'Select Leave Type',
                  items: controller.leaveType,
                  // initialItem: _list[0],
                  onChanged: (value) {
                    print('changing value to: $value');
                    if (value == 'Full') {
                      controller.isFullLeave.value = true;
                      controller.leaveTypeID.value = 1;
                    } else if (value == "Half") {
                      controller.isFullLeave.value = false;
                      controller.leaveTypeID.value = 2;
                    } else {
                      controller.isFullLeave.value = false;
                      controller.leaveTypeID.value = 0;
                    }

                    controller.selectedFromDate.value = null;
                    controller.selectedToDate.value = null;
                    controller.totalDays.value = 0;

                    print(
                        'controller.isFullLeave.value: ${controller.isFullLeave.value}');
                  },
                ),
                Obx(
                  () => Visibility(
                    visible: !controller.isFullLeave.value,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: customButton(
                        text: controller.selectedFromDate.value != null
                            ? controller
                                .formatDate(controller.selectedFromDate.value!)
                            : 'Date',
                        onPressed: () {
                          print("Handle button press here");
                          controller.selectFromDate(context);
                        },
                      ),
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isFullLeave.value,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: customButton(
                        text: controller.selectedFromDate.value != null
                            ? controller
                                .formatDate(controller.selectedFromDate.value!)
                            : 'From Date',
                        onPressed: () {
                          print("Handle button press here");
                          controller.selectFromDate(context);
                        },
                      ),
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isFullLeave.value,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 10),
                      child: customButton(
                        text: controller.selectedToDate.value != null
                            ? controller
                                .formatDate(controller.selectedToDate.value!)
                            : 'To Date',
                        onPressed: () {
                          print("Handle button press here");
                          controller.selectToDate(context);
                        },
                      ),
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isFullLeave.value,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 0),
                      child: CustomTextField(
                        readOnly: true,
                        controller: controller.toDateController,
                        hintText: controller.totalDays.value != null &&
                                controller.totalDays.value > 1
                            ? "${controller.totalDays.value} Days"
                            : controller.totalDays.value != null &&
                                    controller.totalDays.value == 1
                                ? "${controller.totalDays.value} Day"
                                : "Total Days",
                        height: 55,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 10.v),
                CustomTextField(
                  controller: controller.reasonController,
                  hintText: "Enter Reason",
                  readOnly: false,
                  height: 100,
                ),
                SizedBox(height: 10.v),
              ],
            ),
          ),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_add_leave".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "lbl_submit".tr,
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 25.v,
      ),
      onPressed: () {
        addComplainDetails();
      },
    );
  }

  void addComplainDetails() {
    if (controller.leaveTypeID.value == 0) {
      showToastMessage("Please select leave type");
    } else if (controller.leaveTypeID.value == 1 &&
        controller.selectedFromDate.value == null) {
      showToastMessage("Please select from date");
    } else if (controller.leaveTypeID.value == 1 &&
        controller.selectedToDate.value == null) {
      showToastMessage("Please select to date");
    } else if (controller.leaveTypeID.value == 2 &&
        controller.selectedFromDate.value == null) {
      showToastMessage("Please select date");
    } else if (controller.reasonController.text.isEmpty) {
      showToastMessage("Please add leave reason");
    } else {
      print("print add leave to db");
      controller.addLeaveToDB();
    }
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.readOnly = false,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        readOnly: readOnly,
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}

Widget customButton({
  required String text,
  required VoidCallback onPressed,
}) {
  return GestureDetector(
    onTap: onPressed,
    child: Container(
      height: 55,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20),
      alignment: Alignment.centerLeft,
      child: Text(
        text,
        style: CustomTextStyles.titleSmallBluegray900,
      ),
    ),
  );
}
