// ignore_for_file: 

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_holo_date_picker/date_picker.dart';
import 'package:flutter_holo_date_picker/i18n/date_picker_i18n.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/add_route_plan_screen/controller/add_route_plan_controller.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddRoutePlanScreen extends GetWidget<AddRoutePlanController> {
  AddRoutePlanScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 16.h,
            vertical: 15.v,
          ),
          child: Obx(() {
            EasyLoading.dismiss();
            return SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 15.v),
                  CustomDropdown(
                    decoration: CustomDropdownDecoration(
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                      closedErrorBorder: Border.all(color: Colors.red),
                      closedErrorBorderRadius: BorderRadius.circular(12),
                    ),
                    hintText: 'Select route plan type',
                    initialItem: "Select route plan type",
                    items: controller.routePlanTypeList
                        .map((item) => item['name'].toString())
                        .toList(),
                    onChanged: (value) {
                      print('Selected route plan type: $value');

                      final selectedRouteID =
                          controller.getRoutePlanTypeIDFromName('${value}');

                      controller.selectedRoutePlanType.value =
                          selectedRouteID ?? "";

                      controller.dateWiseController.text = "";
                      controller.dateWiseToSave.value = "";
                      controller.dateWiseToShow.value = "";
                      controller.selectedDay.value = "";
                    },
                  ),
                  SizedBox(height: 10.v),
                  Visibility(
                    visible: controller.selectedRoutePlanType.value == "3"
                        ? true
                        : false,
                    child: Column(
                      children: [
                        CustomTextFieldDatewise(
                          controller: controller.dateWiseController,
                          hintText: "Select date",
                          height: 55,
                        ),
                        SizedBox(height: 10.v),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: controller.selectedRoutePlanType.value == "2"
                        ? true
                        : false,
                    child: Column(
                      children: [
                        CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                            closedErrorBorder: Border.all(color: Colors.red),
                            closedErrorBorderRadius: BorderRadius.circular(12),
                          ),
                          hintText: 'Select day',
                          initialItem: 'Select day',
                          items: controller.dayWiseList,
                          onChanged: (value) {
                            print('Selected route plan type: $value');
                            controller.selectedDay.value = "${value}";
                          },
                        ),
                        SizedBox(height: 10.v),
                      ],
                    ),
                  ),
                  CustomDropdown.search(
                    decoration: CustomDropdownDecoration(
                      searchFieldDecoration: SearchFieldDecoration(
                          textStyle: CustomTextStyles.titleSmallBluegray900),
                      noResultFoundStyle:
                          CustomTextStyles.titleSmallBluegray900,
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                    ),
                    hintText: 'Select Route',
                    searchHintText: 'Search Route',
                    items: controller.routePlanBeatList
                        .map((cm) => cm.beat_name!)
                        .toList(),
                    excludeSelected: false,
                    initialItem: 'Select Route',
                    onChanged: (selectedType) {
                      print('Selected Route: $selectedType');
                      final selectedBeatCode =
                          controller.getBeatCodeFromName('${selectedType}');
                      controller.selectedBeatCode.value =
                          selectedBeatCode ?? "";
                    },
                  ),
                  SizedBox(height: 10.v),
                ],
              ),
            );
          }),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Request Route Plan",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "Submit",
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 24.v,
      ),
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus;
        addRoutePlan();
      },
    );
  }

  void addRoutePlan() async {
    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.none)) {
      showToastMessage("Please check your internet connection");
    } else if (controller.selectedRoutePlanType.value.isEmpty) {
      showToastMessage("Please select route plan type");
    } else if (controller.selectedRoutePlanType.value == "2" &&
        controller.selectedDay.value.isEmpty) {
      showToastMessage("Please select day");
    } else if (controller.selectedRoutePlanType.value == "3" &&
        controller.dateWiseToSave.value.isEmpty) {
      showToastMessage("Please select date");
    } else if (controller.selectedBeatCode.isEmpty) {
      showToastMessage("Please select route");
    } else {
      controller.addRoutePlanAPI();
    }
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
        text: newValue.text.toUpperCase(), selection: newValue.selection);
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        keyboardType: keyboardType,
        maxLength: maxLength,
        decoration: InputDecoration(
          counterText: "",
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}

class CustomTextFieldDatewise extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  final Function(bool)? onChanged;

  CustomTextFieldDatewise({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.readOnly = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _showDatePickerPopup(context);
      },
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                readOnly: readOnly,
                style: CustomTextStyles.titleSmallBluegray900,
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle:
                      hintStyle ?? CustomTextStyles.titleSmallBluegray900,
                  contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
                  border: InputBorder.none,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 12),
              child: CustomImageView(
                svgPath: ImageConstant.imgCalendarPrimary,
                height: 20.v,
                width: 20.h,
                margin: EdgeInsets.symmetric(vertical: 3.v),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showDatePickerPopup(BuildContext context) async {
    var datePicked = await DatePicker.showSimpleDatePicker(
      context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2050, 1, 1),
      initialDate: DateTime.now(), 
      dateFormat: "dd-MMM-yyyy",
      locale: DateTimePickerLocale.en_us,
      looping: true,
    );
    if (datePicked != null) {
      print("date value: ${datePicked}");
      String dateValue = datePicked.toString();
      final AddRoutePlanController routeController =
          Get.put(AddRoutePlanController());
      if (controller == routeController.dateWiseController) {
        routeController.dateWiseToShow.value =
            routeController.formatDateTimeToShow(dateValue);
        routeController.dateWiseToSave.value =
            routeController.formatDateTimeToSave(dateValue);
        routeController.dateWiseController.text =
            routeController.dateWiseToShow.value;
      }

      if (controller == routeController.dateWiseController)
        print("dateWiseToShow: ${routeController.dateWiseToShow.value}");
      print("dateWiseToSave: ${routeController.dateWiseToSave.value}");
    }
  }
}
