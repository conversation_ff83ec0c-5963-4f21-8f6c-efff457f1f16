import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'controller/download_report_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class DownloadReportScreen extends GetWidget<DownloadReportController> {
  DownloadReportScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 16.h,
            vertical: 15.v,
          ),
          child: Obx(() {
            return SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                          ),
                          hintText: 'Select Month',
                          initialItem: controller.selectedMonthName.value,
                          items: controller.monthList
                              .map((item) => item['value'] as String)
                              .toList(),
                          onChanged: (selectedType) {
                            print('Selected Month: $selectedType');
                            controller.setSelectedMonth("${selectedType}");
                          },
                        ),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                          ),
                          hintText: 'Select Year',
                          initialItem: controller.yearList[0],
                          items: controller.yearList,
                          onChanged: (selectedType) {
                            print('Selected Year: $selectedType');
                            controller.selectedYear.value = "$selectedType";
                          },
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: 'Select Report',
                        initialItem: 'Select Report',
                        items: controller.reportNameList
                            .map((item) => item['value'] as String)
                            .toList(),
                        onChanged: (selectedType) {
                          print('Selected Report: $selectedType');
                          controller.selectedReportName.value =
                              controller.getKeyFromValue("${selectedType}",
                                      controller.reportNameList) ??
                                  "";
                        },
                      ),
                    ],
                  ),
                  Visibility(
                    visible: controller.selectedReportName.value == "dbr" ||
                            controller.selectedReportName.value == "dsr"
                        ? true
                        : false,
                    child: Column(
                      children: [
                        SizedBox(height: 10.v),
                        CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                          ),
                          hintText: 'Select Report By',
                          initialItem: 'Select Report By',
                          items: controller.reportTypeList
                              .map((item) => item['value'] as String)
                              .toList(),
                          onChanged: (selectedType) {
                            print('Selected Report By: $selectedType');
                            controller.selectedReportType.value =
                                controller.getKeyFromValue("${selectedType}",
                                        controller.reportTypeList) ??
                                    "";
                          },
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: controller.selectedReportName.value == "dbr" ||
                            controller.selectedReportName.value == "dsr"
                        ? true
                        : false,
                    child: Column(
                      children: [
                        SizedBox(height: 10.v),
                        CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                          ),
                          hintText: 'Select Target Type',
                          initialItem: 'Select Target Type',
                          items: controller.targetList
                              .map((item) => item['value'] as String)
                              .toList(),
                          onChanged: (selectedType) {
                            print('Selected Target Type: $selectedType');
                            controller.selectedTargetType
                                .value = controller.getKeyFromValue(
                                    "${selectedType}", controller.targetList) ??
                                "";
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10.v),
                  SizedBox(height: 10.v),
                  Obx(
                    () => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: controller.downloadTypeList.map<Widget>((item) {
                        bool isSelected =
                            controller.selectedDownloadType.value ==
                                item['key'];

                        return Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                // Update the selectedDownloadType when the radio button or text is tapped
                                controller.selectedDownloadType.value =
                                    item['key'];
                              },
                              child: Row(
                                children: [
                                  Radio<String>(
                                    value: item['key'] as String,
                                    groupValue:
                                        controller.selectedDownloadType.value,
                                    onChanged: (selectedType) {
                                      // Update the selectedDownloadType when radio is selected
                                      controller.selectedDownloadType.value =
                                          selectedType!;
                                    },
                                    activeColor: theme.colorScheme.primary,
                                  ),
                                  Text(
                                    item['value'] as String,
                                    style: TextStyle(
                                      color: isSelected
                                          ? theme.colorScheme.primary
                                          : Colors
                                              .black, // Change text color based on selection
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Report",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "Download Report",
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 24.v,
      ),
      onPressed: () {
        downloadReport();
      },
    );
  }

  void downloadReport() {
    if (controller.selectedMonthKey.value == "") {
      showToastMessage("Please select month");
    } else if (controller.selectedYear.value == "") {
      showToastMessage("Please select year");
    } else if ((controller.selectedReportName.value == "dbr" ||
            controller.selectedReportName.value == "dsr") &&
        controller.selectedReportType.value == "") {
      showToastMessage("Please select report type");
    } else if ((controller.selectedReportName.value == "dbr" ||
            controller.selectedReportName.value == "dsr") &&
        controller.selectedTargetType == "") {
      showToastMessage("Please select target type");
    } else if (controller.selectedDownloadType.value == "") {
      showToastMessage("Please select download format");
    } else {
      print("download report called");
      controller.getReport();
    }
  }
}
