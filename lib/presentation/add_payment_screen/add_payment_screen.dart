
import 'dart:io';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

import 'controller/add_payment_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddPaymentScreen extends GetWidget<AddPaymentController> {
  AddPaymentScreen({Key? key})
      : super(
          key: key,
        );

  List<String> _list = [
    'Rule 1',
    'Rule 2',
    'Rule 3',
    'Rule 4',
  ];

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: SafeArea(
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: _buildAppBar(),
          body: SingleChildScrollView(
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(
                horizontal: 16.h,
                vertical: 15.v,
              ),
              child: Obx(() {
                if (controller.customerList.length > 0) {
                  EasyLoading.dismiss();
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(height: 12.v),
                        _buildCategories(context),
                        SizedBox(height: 12.v),
                        Visibility(
                          visible: controller.isFromNormalAddPayment == true
                              ? true
                              : false,
                          child: Column(
                            children: [
                              CustomDropdown(
                                decoration: CustomDropdownDecoration(
                                  expandedFillColor: Colors.white,
                                  expandedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  expandedBorderRadius:
                                      BorderRadius.circular(12),
                                  closedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  closedBorderRadius: BorderRadius.circular(12),
                                  listItemStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  headerStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                ),
                                hintText: 'Select Client Type',
                                initialItem: 'Select Client Type',
                                items: controller.clientTypeList,
                                onChanged: (selectedType) {
                                  print('Selected Client Type: $selectedType');
                                  if (selectedType == "Company") {
                                    controller.selectedClientType.value = 1;
                                  } else if (selectedType == "SS") {
                                    controller.selectedClientType.value = 2;
                                  } else if (selectedType == "Distributor") {
                                    controller.selectedClientType.value = 3;
                                  } else if (selectedType == "Dealer") {
                                    controller.selectedClientType.value = 4;
                                  } else if (selectedType == "Retailer") {
                                    controller.selectedClientType.value = 5;
                                  }
                                },
                              ),
                              SizedBox(height: 10.v),
                              CustomDropdown.search(
                                decoration: CustomDropdownDecoration(
                                  // CustomTextStyles.bodyMediumBluegray700
                                  searchFieldDecoration: SearchFieldDecoration(
                                      textStyle: CustomTextStyles
                                          .titleSmallBluegray900),
                                  noResultFoundStyle:
                                      CustomTextStyles.bodyMediumBluegray700,
                                  hintStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  listItemStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  headerStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  expandedFillColor: Colors.white,
                                  expandedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  expandedBorderRadius:
                                      BorderRadius.circular(12),
                                  closedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  closedBorderRadius: BorderRadius.circular(12),
                                ),
                                hintText: 'Select Client Name',
                                searchHintText: 'Search',
                                items: controller.customerList
                                    .where((customer) =>
                                        customer.cmType ==
                                        controller.selectedClientType.value)
                                    .map((customer) => customer.cmName ?? "")
                                    .toList(),
                                excludeSelected: false,
                                onChanged: (selectedType) {
                                  print('changing value to: $selectedType');
                                  final selectedCustomerID =
                                      controller.getCustomerCodeFromTypeName(
                                          '${selectedType}');
                                  print(
                                      "selectedCustomerID: ${selectedCustomerID}");
                                  controller.selectedClientName.value =
                                      selectedCustomerID ?? "";
                                },
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: controller.isFromNormalAddPayment == true
                              ? false
                              : true,
                          child: Column(
                            children: [
                              CustomDropdown(
                                decoration: CustomDropdownDecoration(
                                  expandedFillColor: Colors.white,
                                  expandedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  expandedBorderRadius:
                                      BorderRadius.circular(12),
                                  closedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  closedBorderRadius: BorderRadius.circular(12),
                                  listItemStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  headerStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                ),
                                hintText: controller.selectedClientType.value ==
                                        1
                                    ? "Company"
                                    : controller.selectedClientType.value == 2
                                        ? "SS"
                                        : controller.selectedClientType.value ==
                                                3
                                            ? "Distributor"
                                            : controller.selectedClientType
                                                        .value ==
                                                    4
                                                ? "Dealer"
                                                : "Retailer",
                                initialItem: controller
                                            .selectedClientType.value ==
                                        1
                                    ? "Company"
                                    : controller.selectedClientType.value == 2
                                        ? "SS"
                                        : controller.selectedClientType.value ==
                                                3
                                            ? "Distributor"
                                            : controller.selectedClientType
                                                        .value ==
                                                    4
                                                ? "Dealer"
                                                : "Retailer",
                                items: null,
                                onChanged: null,
                              ),
                              SizedBox(height: 10.v),
                              CustomDropdown(
                                decoration: CustomDropdownDecoration(
                                  expandedFillColor: Colors.white,
                                  expandedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  expandedBorderRadius:
                                      BorderRadius.circular(12),
                                  closedBorder:
                                      Border.all(color: Colors.grey[300]!),
                                  closedBorderRadius: BorderRadius.circular(12),
                                  listItemStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                  headerStyle:
                                      CustomTextStyles.titleSmallBluegray900,
                                ),
                                hintText:
                                    controller.isFromNormalPaymentName.value,
                                initialItem:
                                    controller.isFromNormalPaymentName.value,
                                items: null,
                                onChanged: null,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10.v),
                        CustomDropdown<String>(
                          decoration: CustomDropdownDecoration(
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                          ),
                          hintText: 'Select Payment Rules',
                          items: controller.paymentConditionList
                              .map((pc) => pc.pc_name ?? "")
                              .toList(),
                          initialItem: 'Select Payment Rules',
                          onChanged: (value) {
                            print('changing value to: $value');

                            controller.selectedPaymentCondition.value =
                                controller
                                    .getPaymentConditionFromID('${value}')!;
                          },
                        ),
                        SizedBox(height: 10.v),
                        Obx(
                          () => Visibility(
                            visible: controller.selectedPaymentType.value == 1
                                ? false
                                : true,
                            child: Column(
                              children: [
                                CustomDropdown.search(
                                  decoration: CustomDropdownDecoration(
                                    searchFieldDecoration:
                                        SearchFieldDecoration(
                                            textStyle: CustomTextStyles
                                                .titleSmallBluegray900),
                                    noResultFoundStyle:
                                        CustomTextStyles.bodyMediumBluegray700,
                                    listItemStyle:
                                        CustomTextStyles.titleSmallBluegray900,
                                    hintStyle:
                                        CustomTextStyles.titleSmallBluegray900,
                                    headerStyle:
                                        CustomTextStyles.titleSmallBluegray900,
                                    expandedFillColor: Colors.white,
                                    expandedBorder:
                                        Border.all(color: Colors.grey[300]!),
                                    expandedBorderRadius:
                                        BorderRadius.circular(12),
                                    closedBorder:
                                        Border.all(color: Colors.grey[300]!),
                                    closedBorderRadius:
                                        BorderRadius.circular(12),
                                  ),
                                  hintText: 'Select Bank',
                                  searchHintText: 'Search',
                                  items: controller.bankList
                                      .map((bank) => bank.bank_name ?? "")
                                      .toList(),
                                  excludeSelected: false,
                                  // initialItem: controller.customerList[0],
                                  onChanged: (selectedType) {
                                    print('changing value to: ${selectedType}');

                                    controller.selectedBankID.value = controller
                                        .getBankNameFromID('${selectedType}')!;
                                  },
                                ),
                                SizedBox(height: 10.v),
                                CustomTextField(
                                  controller: controller.accountNoController,
                                  hintText: "Account Number",
                                  keyboardType: TextInputType.number,
                                  height: 55,
                                ),
                                SizedBox(height: 10.v),
                              ],
                            ),
                          ),
                        ),
                        Obx(
                          () => Visibility(
                            visible: controller.selectedPaymentType.value == 2
                                ? true
                                : false,
                            child: Column(
                              children: [
                                CustomTextField(
                                  controller: controller.chequeNoController,
                                  hintText: "Cheque Number",
                                  keyboardType: TextInputType.number,
                                  height: 55,
                                ),
                                SizedBox(height: 10.v),
                                _buildChequeDate(context),
                                SizedBox(height: 10.v),
                              ],
                            ),
                          ),
                        ),
                        Obx(
                          () => Visibility(
                            visible: controller.selectedPaymentType.value == 3
                                ? true
                                : false,
                            child: Column(
                              children: [
                                CustomTextField(
                                  controller: controller.utrController,
                                  hintText: "UTR Number",
                                  keyboardType: TextInputType.text,
                                  height: 55,
                                ),
                                SizedBox(height: 10.v),
                                _buildDate(context),
                                SizedBox(height: 10.v),
                              ],
                            ),
                          ),
                        ),
                        CustomTextField(
                          controller: controller.amountController,
                          keyboardType: TextInputType.number,
                          hintText: "lbl_amount".tr,
                          height: 55,
                        ),
                        SizedBox(height: 10.v),
                        CustomTextField(
                          controller: controller.descriptionController,
                          hintText: "lbl_description".tr,
                          height: 55,
                        ),
                        SizedBox(height: 10.v),
                        Obx(
                          () => Visibility(
                            visible: controller.selectedPaymentType.value == 2
                                ? true
                                : false,
                            child: Column(
                              children: [
                                _buildComplaintPhoto(context),
                                SizedBox(height: 30.v),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Obx(
                                      () {
                                        final File? image =
                                            controller.imageFile.value;
                                        if (image != null)
                                          return ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12.0),
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                Image.file(
                                                  image,
                                                  height: 250,
                                                ),
                                              ],
                                            ),
                                          );
                                        else
                                          return Text("");
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  EasyLoading.show(status: 'Loading...');
                  return Container();
                }
              }),
            ),
          ),
          bottomNavigationBar: _buildSubmit(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Add Payment",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget paymentDetails(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildCategories(context),
          SingleChildScrollView(
            child: SizedBox(
              child: Column(
                children: <Widget>[
                  SizedBox(height: 12.v),
                  CustomDropdown<String>(
                    decoration: CustomDropdownDecoration(
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                    ),
                    hintText: 'Select Type Rules',
                    items: _list,
                    initialItem: _list[0],
                    onChanged: (value) {
                      print('changing value to: $value');
                    },
                  ),
                  SizedBox(height: 10.v),
                  CustomTextField(
                    controller: controller.clientNameController,
                    hintText: "lbl_amount".tr,
                    height: 55,
                  ),
                  SizedBox(height: 10.v),
                  CustomTextField(
                    controller: controller.descriptionController,
                    hintText: "lbl_description".tr,
                    height: 55,
                  ),
                  SizedBox(height: 10.v),
                ],
              ),
            ),
          ),
          Spacer(),
          CustomElevatedButton(
            text: "lbl_submit".tr,
            margin: EdgeInsets.only(
              left: 7.h,
              right: 11.h,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategories(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: SizedBox(
        height: 43,
        width: MediaQuery.of(context).size.width -
            32, 
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCategoryOption(
              context,
              "Cash",
              ImageConstant.imgCash11,
              controller,
            ),
            _buildCategoryOption(
              context,
              "Cheque",
              ImageConstant.imgCheque1,
              controller,
            ),
            _buildCategoryOption(
              context,
              "Net Banking",
              ImageConstant.imgNetBanking11,
              controller,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryOption(BuildContext context, String option,
      String iconPath, AddPaymentController controller) {
    return GestureDetector(
      onTap: () {
        controller.selectOption(option);
        print("Selected Option: $option");

        if (option == "Cash") {
          controller.selectedPaymentType.value = 1;
        } else if (option == "Cheque") {
          controller.selectedPaymentType.value = 2;
        } else if (option == "Net Banking") {
          controller.selectedPaymentType.value = 3;
        }
      },
      child: Obx(() {
        final isSelected = controller.selectedOption.value == option;
        final textColor = isSelected ? Colors.white : theme.colorScheme.primary;
        final bgColor =
            isSelected ? theme.colorScheme.primary : Colors.transparent;

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8.h, vertical: 6.v),
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: theme.colorScheme.primary),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomImageView(
                imagePath: iconPath,
                height: 27.v,
                width: 33.h,
                color: isSelected ? Colors.white : theme.colorScheme.primary,
              ),
              Text(
                "$option",
                style: TextStyle(
                  color: textColor,
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildDate(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("cheque date tapped");
        showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: theme.colorScheme
                      .primary, 
                ),
              ),
              child: child!,
            );
          },
        ).then((value) {
          if (value != null) {
            print("fromdate value: $value");

            controller.selectedDate.value = controller.formatDate(value);

            controller.finalChequeDate.value =
                controller.formatChequeDate(value);

            print(controller.selectedChequeDate.value);
          }
        });
      },
      child: Container(
        height: 60.v,
        padding: EdgeInsets.symmetric(
          horizontal: 11.h,
          // vertical: 9.v,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.v),
              child: Text(
                controller.selectedDate.value == "Select Date"
                    ? "Select Date"
                    : controller.selectedDate.value.toString(),
                style: CustomTextStyles.titleSmallBlack90015,
              ),
            ),
            CustomImageView(
              svgPath: ImageConstant.imgCalendar,
              color: theme.colorScheme.primary,
              height: 20.adaptSize,
              width: 20.adaptSize,
              margin: EdgeInsets.only(top: 2.v),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChequeDate(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("cheque date tapped");
        showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: theme.colorScheme
                      .primary, 
                ),
              ),
              child: child!,
            );
          },
        ).then((value) {
          if (value != null) {
            print("fromdate value: $value");

            controller.selectedChequeDate.value = controller.formatDate(value);

            controller.finalChequeDate.value =
                controller.formatChequeDate(value);

            print(controller.selectedChequeDate.value);
          }
        });
      },
      child: Container(
        height: 60.v,
        padding: EdgeInsets.symmetric(
          horizontal: 11.h,
          // vertical: 9.v,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.v),
              child: Text(
                controller.selectedChequeDate.value == " Cheque Date"
                    ? "Cheque Date"
                    : controller.selectedChequeDate.value.toString(),
                style: CustomTextStyles.titleSmallBlack90015,
              ),
            ),
            CustomImageView(
              svgPath: ImageConstant.imgCalendar,
              color: theme.colorScheme.primary,
              height: 20.adaptSize,
              width: 20.adaptSize,
              margin: EdgeInsets.only(top: 2.v),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComplaintPhoto(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("payment photo tapped");
        controller.imageFile.value = null;
        _showSelectionBottomSheet(context);
      },
      child: Container(
        height: 60.v,
        padding: EdgeInsets.symmetric(
          horizontal: 11.h,
          // vertical: 9.v,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.v),
              child: Text(
                "Cheque Photo".tr,
                style: CustomTextStyles.titleSmallBlack90015,
              ),
            ),
            CustomImageView(
              imagePath: ImageConstant.imgCameraToTakePhotos,
              height: 20.adaptSize,
              width: 20.adaptSize,
              margin: EdgeInsets.only(top: 2.v),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionBottomSheet(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('Camera'),
                onTap: () {
                  Navigator.of(context).pop();
                  controller.requestCameraPermission();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "lbl_submit".tr,
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 24.v,
      ),
      onPressed: () {
        addPaymentDetails();
      },
    );
  }

  // selectedPaymentCondition

  // controller.selectedDate.value == "Select Date"

  void addPaymentDetails() {
    if (controller.selectedClientType.value == 0) {
      showToastMessage("Please select client type");
    } else if (controller.selectedClientName.value == 0) {
      showToastMessage("Please select customer name");
    } else if (controller.selectedPaymentCondition.value == 0) {
      showToastMessage("Please select payment condition");
    } else if (controller.selectedPaymentType.value != 1 &&
        controller.selectedBankID.value == 0) {
      showToastMessage("Please select bank");
    } else if (controller.selectedPaymentType.value != 1 &&
        controller.accountNoController.text.isEmpty) {
      showToastMessage("Please add account number");
    } else if (controller.selectedPaymentType.value == 2 &&
        controller.chequeNoController.text.isEmpty) {
      showToastMessage("Please add cheque number");
    } else if (controller.selectedPaymentType.value == 2 &&
        controller.selectedChequeDate.value == "Cheque Date") {
      showToastMessage("Please select cheque date");
    } else if (controller.selectedPaymentType.value == 3 &&
        controller.utrController.text.isEmpty) {
      showToastMessage("Please add UTR number");
    } else if (controller.selectedPaymentType.value == 3 &&
        controller.selectedDate.value == "Select Date") {
      showToastMessage("Please select date");
    } else if (controller.amountController.text.isEmpty) {
      showToastMessage("Please add amount");
    } else if (controller.descriptionController.text.isEmpty) {
      showToastMessage("Please add description");
    } else if (controller.selectedPaymentType.value == 2 &&
        controller.imageFile.value.isBlank!) {
      showToastMessage("Please add cheque photo");
    } else {
      print("add payment to db");
      showToastMessage("Adding payment");
      controller.addPaymentToDB();
    }
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType keyboardType; 

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType = TextInputType.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        keyboardType: keyboardType, 
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}

class GetPermissions {
  static Future<bool> getCameraPermission() async {
    PermissionStatus permissionStatus = await Permission.camera.status;
    if (permissionStatus.isGranted) {
      return true;
    } else if (permissionStatus.isDenied) {
      PermissionStatus status = await Permission.camera.request();
      if (status.isGranted) {
        return true;
      } else {
        showToastMessage("Camera permission is required");
        return false;
      }
    } else {
      return false;
    }
  }

  static Future<bool> getStoragePermission() async {
    PermissionStatus permissionStatus = await Permission.storage.status;
    if (permissionStatus.isGranted) {
      return true;
    } else if (permissionStatus.isDenied) {
      PermissionStatus status = await Permission.storage.request();
      if (status.isGranted) {
        return true;
      } else {
        showToastMessage("Storage permission is required");
        return false;
      }
    } else {
      return false;
    }
  }

  static Future<bool> getGalleryPermission() async {
    PermissionStatus permissionStatus = await Permission.photos.status;
    if (permissionStatus.isGranted) {
      return true;
    } else if (permissionStatus.isDenied) {
      PermissionStatus status = await Permission.photos.request();
      if (status.isGranted) {
        return true;
      } else {
        showToastMessage("Photo permission is required");
        return false;
      }
    } else {
      return false;
    }
  }
}
