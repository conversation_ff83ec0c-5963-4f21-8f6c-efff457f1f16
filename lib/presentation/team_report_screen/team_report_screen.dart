// ignore_for_file: invalid_use_of_protected_member, unnecessary_null_comparison

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_holo_date_picker/date_picker.dart';
import 'package:flutter_holo_date_picker/i18n/date_picker_i18n.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/presentation/team_report_screen/controller/team_report_controller.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

class TeamReportScreen extends GetWidget<TeamReportController> {
  TeamReportScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 16.h,
            vertical: 15.v,
          ),
          child: Obx(() {
            return SingleChildScrollView(
              child: Column(
                children: [
                  CustomDropdown(
                    decoration: CustomDropdownDecoration(
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                    ),
                    hintText: 'Select Team Member',
                    initialItem: 'Select Team Member',
                    items: controller.teamMembers
                        .map((item) => item.empName ?? "")
                        .toList(),
                    onChanged: (selectedType) {
                      print('Selected Team Member: $selectedType');
                      controller.saveEmpCode("${selectedType}");
                    },
                  ),
                  SizedBox(height: 8),
                  CustomDropdown(
                    decoration: CustomDropdownDecoration(
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                    ),
                    hintText: 'Select Report',
                    initialItem: 'Select Report',
                    items: controller.reportTypeList
                        .map((item) => item.title)
                        .toList(),
                    onChanged: (selectedReport) {

                      print('Selected Report: $selectedReport');
                      controller.updateCustomerDropdown("${selectedReport}");
                    },
                  ),
                  Visibility(
                    visible: controller.showCustomerDropdown.value,
                    child: Column(
                      children: [
                        SizedBox(height: 8),
                        CustomDropdown(
                          decoration: CustomDropdownDecoration(
                            expandedFillColor: Colors.white,
                            expandedBorder:
                                Border.all(color: Colors.grey[300]!),
                            expandedBorderRadius: BorderRadius.circular(12),
                            closedBorder: Border.all(color: Colors.grey[300]!),
                            closedBorderRadius: BorderRadius.circular(12),
                            listItemStyle:
                                CustomTextStyles.titleSmallBluegray900,
                            headerStyle: CustomTextStyles.titleSmallBluegray900,
                          ),
                          hintText: 'Select Customer',
                          initialItem: 'Select Customer',
                          items: controller.customerTypes
                              .map((item) => item.title)
                              .toList(),
                          onChanged: (selectedCustomer) {
                            print('Selected Customer: $selectedCustomer');
                            controller.saveCustomerType("${selectedCustomer}");
                          },
                        ),
                      ],
                    ),
                  ),
                  // selectedReportType
                  SizedBox(height: 8),
                  CustomTextFieldDateRange(
                    controller: controller.fromDateController,
                    hintText: "From Date",
                    height: 55,
                  ),
                  SizedBox(height: 8),
                  CustomTextFieldDateRange(
                    controller: controller.toDateController,
                    hintText: "To Date",
                    height: 55,
                  ),
                  SizedBox(height: 8),
                ],
              ),
            );
          }),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Team Report",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "Download Report",
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 24.v,
      ),
      onPressed: () {
        downloadReport();
      },
    );
  }

  void downloadReport() {
    if (controller.selectedEmpCode.value == "") {
      showToastMessage("Please select team member");
    } else if (controller.selectedReportType.value == "") {
      showToastMessage("Please select report type");
    } else if (controller.showCustomerDropdown.value == true &&
        controller.selectedCustomerType.value == "") {
      showToastMessage("Please select report type");
    } else if (controller.fromDateToSave.value.isEmpty) {
      showToastMessage("Please select from date");
    } else if (controller.toDateToSave.value.isEmpty) {
      showToastMessage("Please select to date");
    } else {
      print("download team report called");
      controller.postTeamReportAPI();
    }
  }
}

class CustomTextFieldDateRange extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  final Function(bool)? onChanged;

  CustomTextFieldDateRange({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.readOnly = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _showDatePickerPopup(context);
      },
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                readOnly: readOnly,
                style: CustomTextStyles.titleSmallBluegray900,
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle:
                      hintStyle ?? CustomTextStyles.titleSmallBluegray900,
                  contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
                  border: InputBorder.none,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 12),
              child: CustomImageView(
                svgPath: ImageConstant.imgCalendarPrimary,
                height: 20.v,
                width: 20.h,
                margin: EdgeInsets.symmetric(vertical: 3.v),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showDatePickerPopup(BuildContext context) async {
    var datePicked = await DatePicker.showSimpleDatePicker(
      context,
      firstDate: DateTime(1950, 1, 1),
      lastDate: DateTime(2050, 1, 1),
      initialDate: DateTime.now(), 
      dateFormat: "dd-MMM-yyyy",
      locale: DateTimePickerLocale.en_us,
      looping: true,
    );
    if (datePicked != null) {
      print("date value: ${datePicked}");
      String dateValue = datePicked.toString();
      final TeamReportController retailerController =
          Get.put(TeamReportController());
      if (controller == retailerController.fromDateController) {
        retailerController.fromDateToShow.value =
            retailerController.formatDateTimeToShow(dateValue);
        retailerController.fromDateToSave.value =
            retailerController.formatDateTimeToSave(dateValue);

        retailerController.fromDateController.text =
            retailerController.fromDateToShow.value;

        print("fromDateToShow: ${retailerController.fromDateToShow.value}");
        print("fromDateToSave: ${retailerController.fromDateToSave.value}");
      } else {
        retailerController.toDateToShow.value =
            retailerController.formatDateTimeToShow(dateValue);
        retailerController.toDateToSave.value =
            retailerController.formatDateTimeToSave(dateValue);

        retailerController.toDateController.text =
            retailerController.toDateToShow.value;

        print("toDateToShow: ${retailerController.toDateToShow.value}");
        print("toDateToSave: ${retailerController.toDateToSave.value}");
      }
    }
  }
}
