
import 'dart:io';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'controller/add_complain_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddComplainScreen extends GetWidget<AddComplainController> {
  AddComplainScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 16.h,
              vertical: 15.v,
            ),
            child: Obx(() {
              if (controller.customerList.length > 0) {
                EasyLoading.dismiss();
                return SingleChildScrollView(
                  child: Column(
                    children: [
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: 'lbl',
                        initialItem: 'Select Claim/Complain',
                        items: controller.claimComplainList,
                        onChanged: (selectedType) {
                          print('Selected Claim/Complain: $selectedType');
                          if (selectedType == "Claim") {
                            controller.selectedClaimComplain.value = 1;
                          } else {
                            controller.selectedClaimComplain.value = 2;
                          }
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: controller.selectedClaimComplain.value == 1
                            ? 'Select Claim Type'
                            : 'Select Complain Type',
                        initialItem: controller.selectedClaimComplain.value == 1
                            ? 'Select Claim Type'
                            : 'Select Complain Type',
                        items: controller.complainTypeList
                            .where((ClaimComplainType) =>
                                ClaimComplainType.cct_type_status ==
                                controller.selectedClaimComplain.value)
                            .map((claimComplainType) =>
                                claimComplainType.cct_type_name!)
                            .toList(),
                        onChanged: (selectedType) {
                          print('Selected Claim/Complain Type: $selectedType');
                          final selectedClaimComplainID =
                              controller.getClaimComplainIdFromTypeName(
                                  '${selectedType}');

                          controller.selectedCCType.value =
                              selectedClaimComplainID ?? 0;
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown(
                        decoration: CustomDropdownDecoration(
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                        ),
                        hintText: 'lbl_select_client_type'.tr,
                        initialItem: 'lbl_select_client_type'.tr,
                        items: controller.clientTypeList,
                        onChanged: (selectedType) {
                          print('Selected Client Type: $selectedType');
                          if (selectedType == "Company") {
                            controller.selectedClientType.value = 1;
                          } else if (selectedType == "SS") {
                            controller.selectedClientType.value = 2;
                          } else if (selectedType == "Distributor") {
                            controller.selectedClientType.value = 3;
                          } else if (selectedType == "Dealer") {
                            controller.selectedClientType.value = 4;
                          } else if (selectedType == "Retailer") {
                            controller.selectedClientType.value = 5;
                          }
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomDropdown.search(
                        decoration: CustomDropdownDecoration(
                          searchFieldDecoration: SearchFieldDecoration(
                              textStyle:
                                  CustomTextStyles.titleSmallBluegray900),
                          noResultFoundStyle:
                              CustomTextStyles.bodyMediumBluegray700,
                          listItemStyle: CustomTextStyles.titleSmallBluegray900,
                          headerStyle: CustomTextStyles.titleSmallBluegray900,
                          expandedFillColor: Colors.white,
                          expandedBorder: Border.all(color: Colors.grey[300]!),
                          expandedBorderRadius: BorderRadius.circular(12),
                          closedBorder: Border.all(color: Colors.grey[300]!),
                          closedBorderRadius: BorderRadius.circular(12),
                        ),
                        hintText: 'Select Client Name',
                        initialItem: 'Select Client Name',
                        searchHintText: 'Search',
                        items: controller.customerList
                            .where((customer) =>
                                customer.cmType ==
                                controller.selectedClientType.value)
                            .map((customer) => customer.cmName ?? "")
                            .toList(),
                        excludeSelected: false,
                        onChanged: (selectedType) {
                          print(
                              "selectedClientType: ${controller.selectedClientType}");
                          print('changing value to: $selectedType');
                          final selectedCustomerID = controller
                              .getCustomerCodeFromTypeName('${selectedType}');
                          print("selectedCustomerID: ${selectedCustomerID}");
                          controller.selectedClientName.value =
                              selectedCustomerID ?? '';
                        },
                      ),
                      SizedBox(height: 10.v),
                      CustomTextField(
                        controller: controller.descriptionController,
                        hintText: "Please add description",
                        height: 100,
                      ),
                      SizedBox(height: 10.v),
                      _buildComplaintPhoto(context),
                      SizedBox(height: 30.v),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Obx(
                            () {
                              final File? image = controller.imageFile.value;
                              if (image != null)
                                return ClipRRect(
                                  borderRadius: BorderRadius.circular(12.0),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      Image.file(
                                        image,
                                        height: 250,
                                      ),
                                    ],
                                  ),
                                );
                              else
                                return Text("");
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              } else {
                EasyLoading.show(status: 'Loading...');
                return Container();
              }
            }),
          ),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_add_complain".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildComplaintPhoto(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("claim/complaint photo tapped");
        FocusManager.instance.primaryFocus?.unfocus();
        controller.imageFile.value = null;
        _showSelectionBottomSheet(context);
      },
      child: Container(
        height: 60.v,
        padding: EdgeInsets.symmetric(
          horizontal: 11.h,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.v),
              child: Text(
                "Claim/Complain Photo",
                style: CustomTextStyles.titleSmallBluegray900,
              ),
            ),
            CustomImageView(
              imagePath: ImageConstant.imgCameraToTakePhotos,
              height: 20.adaptSize,
              width: 20.adaptSize,
              margin: EdgeInsets.only(top: 2.v),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionBottomSheet(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('Photo/Files'),
                onTap: () {
                  Navigator.of(context).pop();
                  controller.pickImageNew(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: Icon(Icons.camera),
                title: Text('Camera'),
                onTap: () {
                  Navigator.of(context).pop();
                  controller.pickImageNew(ImageSource.camera);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "lbl_submit".tr,
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 24.v,
      ),
      onPressed: () {
        addComplainDetails();
      },
    );
  }

  void addComplainDetails() {
    print("controller.imageFile: ${controller.imageFile}");

    // return;

    if (controller.selectedClaimComplain.value == 0) {
      showToastMessage("Please select claim/complain");
    } else if (controller.selectedCCType.value == 0) {
      showToastMessage("Please select claim/complain type");
    } else if (controller.selectedClientType.value == 0) {
      showToastMessage("Please select client type");
    } else if (controller.selectedClientName.value == "") {
      showToastMessage("Please select client name");
    } else if (controller.descriptionController.text.isEmpty) {
      showToastMessage("Please add description");
      // }
      // else if (controller.imageFile.value == null) {
      //   showToastMessage("Please add claim/complain photo");
    } else {
      print("print add complain to db");
      controller.addComplainToDB();
    }
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
