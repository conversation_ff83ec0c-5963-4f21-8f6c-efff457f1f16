import 'dart:io';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';

import 'controller/add_expense_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class AddExpenseScreen extends GetWidget<AddExpenseController> {
  AddExpenseScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 16.h,
              vertical: 15.v,
            ),
            child: Obx(
              () => Column(
                children: [
                  CustomDropdown(
                    decoration: CustomDropdownDecoration(
                      expandedFillColor: Colors.white,
                      expandedBorder: Border.all(color: Colors.grey[300]!),
                      expandedBorderRadius: BorderRadius.circular(12),
                      closedBorder: Border.all(color: Colors.grey[300]!),
                      closedBorderRadius: BorderRadius.circular(12),
                      listItemStyle: CustomTextStyles.titleSmallBluegray900,
                      headerStyle: CustomTextStyles.titleSmallBluegray900,
                    ),
                    hintText: 'Select Expense Type',
                    initialItem: 'Select Expense Type',
                    items: controller.expenseTypeList
                        .map((expenseType) => expenseType.expenseName ?? "")
                        .toList(),
                    // initialItem: _list[0],
                    onChanged: (value) {
                      print('changing value to: $value');
                      final selectedExpenseID =
                          controller.getExpenseCodeFromTypeName('${value}');
                      print("selectedCustomerID: ${selectedExpenseID}");
                      controller.selectedExpenseType.value =
                          selectedExpenseID ?? 0;
                    },
                  ),
                  SizedBox(height: 10.v),
                  CustomTextField(
                    controller: controller.detailsController,
                    hintText: "lbl_details".tr,
                    height: 55,
                  ),
                  SizedBox(height: 10.v),
                  CustomTextField(
                    controller: controller.amountController,
                    keyboardType: TextInputType.number,
                    hintText: "lbl_amount".tr,
                    height: 55,
                  ),
                  SizedBox(height: 10.v),
                  _buildExpensePhoto(context),
                  SizedBox(height: 20.v),
                  Visibility(
                    visible: controller.pdfSelected.value == 1 ? true : false,
                    child: Text(
                      "Selected File : ${controller.pdfFileName.value}",
                      style: CustomTextStyles.titleSmallBluegray900,
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Obx(
                        () {
                          final File? image = controller.imageFile.value;
                          if (image != null)
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(12.0),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Image.file(
                                    image,
                                    height: 250,
                                  ),
                                ],
                              ),
                            );
                          else
                            return Container(
                              height: 250,
                              child: Center(
                                child: Text(
                                  "",
                                  style: CustomTextStyles.titleSmallBluegray900,
                                ),
                              ),
                            );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_add_expense".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildExpensePhoto(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
        print("expense photo tapped");
        controller.imageFile.value = null;
        _showSelectionBottomSheet(context);
      },
      child: Container(
        height: 60.v,
        padding: EdgeInsets.symmetric(
          horizontal: 11.h,
          // vertical: 9.v,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10.0)),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.v),
              child: Text(
                "Expense Photo/File",
                style: CustomTextStyles.titleSmallBluegray900,
              ),
            ),
            CustomImageView(
              imagePath: ImageConstant.imgCameraToTakePhotos,
              height: 20.adaptSize,
              width: 20.adaptSize,
              margin: EdgeInsets.only(top: 2.v),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionBottomSheet(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
          child: Container(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: Icon(Icons.photo_library),
                  title: Text('Photo/Files'),
                  onTap: () {
                    Navigator.of(context).pop();
                    controller.pickImageNew(ImageSource.gallery);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.camera),
                  title: Text('Camera'),
                  onTap: () {
                    controller.pdfSelected.value = 0;
                    Navigator.of(context).pop();
                    controller.pickImageNew(ImageSource.camera);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubmit() {
    // exp_min_amount_req_image

    return CustomElevatedButton(
      text: "lbl_submit".tr,
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 28.v,
      ),
      onPressed: () {
        int textAmt = int.tryParse(controller.amountController.text) ?? 0;
        print("textAmt: ${textAmt}");
        int expenseAmt =
            int.tryParse(controller.expenseAmountForReqImage.value) ?? 0;
        print("expenseAmt: ${expenseAmt}");

        if (controller.selectedExpenseType.value == 0) {
          showToastMessage("Please select expense type");
        } else if (controller.detailsController.text.isEmpty) {
          showToastMessage("Please add expense details");
        } else if (controller.amountController.text.isEmpty) {
          showToastMessage("Please add amount");
        } else if ((textAmt >= expenseAmt) &&
            (controller.imageFile.value == null)) {
          showToastMessage("Please add expense bill photo");
        } else {
          print("print add expense api");
          showToastMessage("Adding expense");
          controller.addExpenseToDB();
        }
      },
    );
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
